import 'dart:io';
import 'dart:typed_data';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:saver_gallery/saver_gallery.dart';
import 'package:wallhammer/components/icon_text_button.dart';
import 'package:wallhammer/components/star_rating.dart';
import 'package:wallhammer/components/wallpaper_network_image.dart';
import 'package:wallhammer/components/wallpaper_overview_item.dart';
import 'package:wallhammer/components/wallpaper_rating_dialog.dart';
import 'package:wallhammer/model/wallpaper.dart';

import 'full_screen_image_screen.dart';


class WallpaperDetailScreen extends StatefulWidget {
  final Wallpaper wallpaper;
  final List<Wallpaper> dataset;

  const WallpaperDetailScreen({
    required this.wallpaper,
    required this.dataset,
    super.key,
  });

  @override
  State<WallpaperDetailScreen> createState() => _WallpaperDetailScreenState();
}

class _WallpaperDetailScreenState extends State<WallpaperDetailScreen> {
  bool isDownloading = false;
  bool isFullScreen = false; // Track full screen state

  late final List<Wallpaper> similarWallpapers = _getSimilarWallpapers();

  @override
  void dispose() {
    // Restore status bar when leaving the screen
    _showSystemUI();
    super.dispose();
  }

  void _hideSystemUI() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  }

  void _showSystemUI() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Main Wallpaper
          WallpaperNetworkImage(
            wallpaper: widget.wallpaper,
            height: double.infinity,
          ),

          // Dark overlay for UI visibility (hidden in full screen) - also tappable
          if (!isFullScreen)
            GestureDetector(
              onTap: () {
                print('🔄 Toggling to full screen mode');
                _hideSystemUI();
                setState(() {
                  isFullScreen = !isFullScreen;
                });
              },
              child: Container(color: Colors.black.withOpacity(0.4)),
            ),

          // Full screen tap detector (when no overlay)
          if (isFullScreen)
            GestureDetector(
              onTap: () {
                print('🔄 Toggling back to normal mode');
                _showSystemUI();
                setState(() {
                  isFullScreen = !isFullScreen;
                });
              },
              child: Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.transparent,
              ),
            ),

          // Top info bar (hidden in full screen)
          if (!isFullScreen)
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              left: 16,
              right: 16,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const BackButton(color: Colors.white),
                  Row(
                    children: [
                      const Icon(Icons.download, color: Colors.white70),
                      const SizedBox(width: 4),
                      Text(widget.wallpaper.downloadCount.toString(),
                          style: const TextStyle(color: Colors.white)),
                    ],
                  )
                ],
              ),
            ),

          // Center controls (hidden in full screen)
          if (!isFullScreen)
            Positioned(
              bottom: similarWallpapers.isNotEmpty ? 160 : 40, // Position above similar wallpapers
              left: 0,
              right: 0,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (!isDownloading)
                    IconButton(
                      icon: const Icon(Icons.download_rounded, color: Colors.white),
                      iconSize: 48,
                      onPressed: _handleDownload,
                    )
                  else
                    const CircularProgressIndicator(color: Colors.white),

                  const SizedBox(height: 20),

                  StarRating(rating: widget.wallpaper.averageRating),
                  Text(
                    'Rating: ${widget.wallpaper.averageRating.toStringAsFixed(1)}',
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),

          // Similar wallpapers (hidden in full screen)
          if (similarWallpapers.isNotEmpty && !isFullScreen)
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: SizedBox(
                height: 120,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  children: similarWallpapers
                      .map((e) => Padding(
                    padding: const EdgeInsets.only(right: 10),
                    child: WallpaperOverviewItem(
                      wallpaper: e,
                      dataset: widget.dataset,
                    ),
                  ))
                      .toList(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _handleDownload() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    setState(() => isDownloading = true);

    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      scaffoldMessenger.showSnackBar(const SnackBar(
          content: Text('You must be logged in to download wallpapers.')));
      setState(() => isDownloading = false);
      return;
    }

    final userRef = FirebaseFirestore.instance.collection('users').doc(user.uid);
    final wallpaperRef =
    FirebaseFirestore.instance.collection('wallpapers').doc(widget.wallpaper.id);

    try {
      final userSnapshot = await userRef.get();
      if (!userSnapshot.exists) {
        scaffoldMessenger.showSnackBar(
            const SnackBar(content: Text('User not found in database.')));
        setState(() => isDownloading = false);
        return;
      }

      final userData = userSnapshot.data();
      int userCoins = userData?['coins'] ?? 0;

      if (userCoins < 1) {
        scaffoldMessenger.showSnackBar(
            const SnackBar(content: Text('Not enough coins to download.')));
        setState(() => isDownloading = false);
        return;
      }

      final imageUrl = await widget.wallpaper.fetchImageUrl();
      if (imageUrl?.isEmpty ?? true) {
        scaffoldMessenger.showSnackBar(
            const SnackBar(content: Text('No valid download found.')));
        return;
      }

      final success = await _downloadWallpaper(imageUrl!);

      if (success) {
        await wallpaperRef.update({'downloads': FieldValue.increment(1)});
        await userRef.update({'coins': FieldValue.increment(-1)});

        scaffoldMessenger.showSnackBar(
          const SnackBar(content: Text('Wallpaper downloaded successfully.')),
        );

        if (context.mounted) {
          showDialog(
            context: context,
            builder: (context) => WallpaperRatingDialog(
              onRated: (rating) => _updateWallpaperRating(widget.wallpaper, rating),
            ),
          );
        }
      } else {
        scaffoldMessenger.showSnackBar(
          const SnackBar(content: Text('Failed to download wallpaper.')),
        );
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(SnackBar(content: Text('Error: $e')));
    }

    setState(() => isDownloading = false);
  }

  Future<bool> _downloadWallpaper(String imageUrl) async {
    bool hasPermission = await checkAndRequestPermissions(skipIfExists: false);
    if (!hasPermission) return false;

    final directory = await getTemporaryDirectory();
    final fileName = widget.wallpaper.filePath.split('/').last;
    final filePath = '${directory.path}/wallpaper_$fileName';

    try {
      final dio = Dio();
      final response = await dio.get(imageUrl, options: Options(responseType: ResponseType.bytes));
      final result = await SaverGallery.saveImage(
        Uint8List.fromList(response.data),
        fileName: "wallpaper_$fileName",
        skipIfExists: true,
      );

      return result.isSuccess;
    } catch (e) {
      return false;
    }
  }

  Future<bool> checkAndRequestPermissions({required bool skipIfExists}) async {
    if (!Platform.isAndroid && !Platform.isIOS) {
      return false;
    }

    if (Platform.isAndroid) {
      final deviceInfo = await DeviceInfoPlugin().androidInfo;
      final sdkInt = deviceInfo.version.sdkInt;

      if (skipIfExists) {
        return sdkInt >= 33
            ? await Permission.photos.request().isGranted
            : await Permission.storage.request().isGranted;
      } else {
        return sdkInt >= 29 ? true : await Permission.storage.request().isGranted;
      }
    } else if (Platform.isIOS) {
      return skipIfExists
          ? await Permission.photos.request().isGranted
          : await Permission.photosAddOnly.request().isGranted;
    }

    return false;
  }

  List<Wallpaper> _getSimilarWallpapers() {
    return widget.dataset
        .where((e1) =>
    e1.categories.any((e2) => widget.wallpaper.categories.contains(e2)) &&
        e1 != widget.wallpaper)
        .toList();
  }

  void _updateWallpaperRating(Wallpaper wallpaper, double rating) {
    setState(() {
      wallpaper.ratings.add(rating);
      wallpaper.averageRating =
          wallpaper.ratings.reduce((a, b) => a + b) / wallpaper.ratings.length;
    });

    FirebaseFirestore.instance.collection('wallpapers').doc(wallpaper.id).update({
      'ratings': wallpaper.ratings,
      'averageRating': wallpaper.averageRating,
    });
  }
}
